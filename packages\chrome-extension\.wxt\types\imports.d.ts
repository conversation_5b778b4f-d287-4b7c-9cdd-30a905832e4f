// Generated by wxt
export {}
declare global {
  const CodeBlock: typeof import('G:/i2c-server/packages/chrome-extension/components/CodeBlock')['default']
  const CodeSection: typeof import('G:/i2c-server/packages/chrome-extension/components/CodeSection')['default']
  const ContentScriptContext: typeof import('wxt/client')['ContentScriptContext']
  const InvalidMatchPattern: typeof import('wxt/sandbox')['InvalidMatchPattern']
  const MatchPattern: typeof import('wxt/sandbox')['MatchPattern']
  const MigrationError: typeof import('wxt/storage')['MigrationError']
  const PluginInstaller: typeof import('G:/i2c-server/packages/chrome-extension/components/PluginInstaller')['default']
  const PluginItem: typeof import('G:/i2c-server/packages/chrome-extension/components/PluginItem')['default']
  const PluginLoadingTest: typeof import('G:/i2c-server/packages/chrome-extension/components/PluginLoadingTest')['default']
  const PluginsSection: typeof import('G:/i2c-server/packages/chrome-extension/components/PluginsSection')['default']
  const base64ToBlob: typeof import('G:/i2c-server/packages/chrome-extension/utils/common-utils')['base64ToBlob']
  const browser: typeof import('wxt/browser/chrome')['browser']
  const compressImg: typeof import('G:/i2c-server/packages/chrome-extension/utils/common-utils')['compressImg']
  const compressImgWithTinyPNG: typeof import('G:/i2c-server/packages/chrome-extension/utils/common-utils')['compressImgWithTinyPNG']
  const copyToClipboard: typeof import('G:/i2c-server/packages/chrome-extension/utils/copyClipboard')['copyToClipboard']
  const createIframeUi: typeof import('wxt/client')['createIframeUi']
  const createIntegratedUi: typeof import('wxt/client')['createIntegratedUi']
  const createShadowRootUi: typeof import('wxt/client')['createShadowRootUi']
  const defineAppConfig: typeof import('wxt/sandbox')['defineAppConfig']
  const defineBackground: typeof import('wxt/sandbox')['defineBackground']
  const defineConfig: typeof import('wxt')['defineConfig']
  const defineContentScript: typeof import('wxt/sandbox')['defineContentScript']
  const defineUnlistedScript: typeof import('wxt/sandbox')['defineUnlistedScript']
  const defineWxtPlugin: typeof import('wxt/sandbox')['defineWxtPlugin']
  const doApplyUpload: typeof import('G:/i2c-server/packages/chrome-extension/utils/bos')['doApplyUpload']
  const doBosUpload: typeof import('G:/i2c-server/packages/chrome-extension/utils/bos')['doBosUpload']
  const downloadFile: typeof import('G:/i2c-server/packages/chrome-extension/utils/common-utils')['downloadFile']
  const exportBase64Image: typeof import('G:/i2c-server/packages/chrome-extension/utils/common-utils')['exportBase64Image']
  const fakeBrowser: typeof import('wxt/testing')['fakeBrowser']
  const fixNum: typeof import('G:/i2c-server/packages/chrome-extension/utils/format-number-utils')['fixNum']
  const getFloat: typeof import('G:/i2c-server/packages/chrome-extension/utils/format-number-utils')['getFloat']
  const getImgFormat: typeof import('G:/i2c-server/packages/chrome-extension/utils/common-utils')['getImgFormat']
  const getInt: typeof import('G:/i2c-server/packages/chrome-extension/utils/format-number-utils')['getInt']
  const getNumericFloat: typeof import('G:/i2c-server/packages/chrome-extension/utils/format-number-utils')['getNumericFloat']
  const getNums: typeof import('G:/i2c-server/packages/chrome-extension/utils/format-number-utils')['getNums']
  const getObjType: typeof import('G:/i2c-server/packages/chrome-extension/utils/common-utils')['getObjType']
  const injectScript: typeof import('wxt/client')['injectScript']
  const isDev: typeof import('G:/i2c-server/packages/chrome-extension/utils/env')['isDev']
  const storage: typeof import('wxt/storage')['storage']
  const upload: typeof import('G:/i2c-server/packages/chrome-extension/utils/bos')['upload']
  const useAppConfig: typeof import('wxt/client')['useAppConfig']
  const useCallback: typeof import('react')['useCallback']
  const useCodeTransform: typeof import('G:/i2c-server/packages/chrome-extension/hooks/useCodeTransform')['useCodeTransform']
  const useContext: typeof import('react')['useContext']
  const useEffect: typeof import('react')['useEffect']
  const useMemo: typeof import('react')['useMemo']
  const usePluginExecutor: typeof import('G:/i2c-server/packages/chrome-extension/hooks/usePluginExecutor')['usePluginExecutor']
  const usePluginInstall: typeof import('G:/i2c-server/packages/chrome-extension/hooks/usePluginInstall')['usePluginInstall']
  const useReducer: typeof import('react')['useReducer']
  const useRef: typeof import('react')['useRef']
  const useState: typeof import('react')['useState']
  const withTimeout: typeof import('G:/i2c-server/packages/chrome-extension/utils/common-utils')['withTimeout']
}
// for type re-export
declare global {
  // @ts-ignore
  export type { PluginData } from 'G:/i2c-server/packages/chrome-extension/hooks/usePluginInstall'
  import('G:/i2c-server/packages/chrome-extension/hooks/usePluginInstall')
}
