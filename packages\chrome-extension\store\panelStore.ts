import {BaseStore} from './baseStore'

export class PanelStore extends BaseStore {
  constructor() {
    super()
    this.init()
  }

  isExpend = true
  position = {x: 0, y: 0}
  lockDeepSelection = false
  lockLockAltKey = false

  private init() {
    const savedState = localStorage.getItem('f2c:pannelSettings')
    if (savedState !== null) {
      const parsed = JSON.parse(savedState)
      this.isExpend = parsed.isExpend
      this.position = parsed.position || this.getDefaultPosition()
    } else {
      // 如果没有保存状态，设置默认位置为右上角
      this.position = this.getDefaultPosition()
    }
  }

  private getDefaultPosition() {
    // 获取窗口尺寸，设置面板在右上角
    // 假设面板宽度约为 300px，留一些边距
    const panelWidth = 300
    const margin = 20

    return {
      x: window.innerWidth - panelWidth - margin,
      y: margin,
    }
  }

  setIsExpend(value: boolean) {
    this.isExpend = value
    this.saveToLocalStorage()
  }

  setPosition(x: number, y: number) {
    this.position = {x, y}
    this.saveToLocalStorage()
  }

  setLockDeepSelection(lock: boolean) {
    this.lockDeepSelection = lock
  }

  setLockAltKey(lock: boolean) {
    this.lockLockAltKey = lock
  }

  private saveToLocalStorage() {
    localStorage.setItem(
      'f2c:pannelSettings',
      JSON.stringify({
        isExpend: this.isExpend,
        position: this.position,
      }),
    )
  }
}

const panelStore = new PanelStore()
export default panelStore
