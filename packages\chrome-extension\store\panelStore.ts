import {setDeepSelectionKey, setLockAltKey as setLockAltKeyFunction} from '../lib/keylock'
import {BaseStore} from './baseStore'

export class PanelStore extends BaseStore {
  constructor() {
    super()
    this.init()
  }

  isExpend = true
  position = {x: 0, y: 0}
  lockDeepSelection = false
  lockLockAltKey = false

  private init() {
    const savedState = localStorage.getItem('f2c:pannelSettings')
    if (savedState !== null) {
      const parsed = JSON.parse(savedState)
      this.isExpend = parsed.isExpend
      this.position = parsed.position || this.getDefaultPosition()
      this.lockDeepSelection = parsed.lockDeepSelection || false
      this.lockLockAltKey = parsed.lockLockAltKey || false

      // 根据保存的状态恢复键盘锁定
      if (this.lockDeepSelection) {
        setDeepSelectionKey(true)
      }
      if (this.lockLockAltKey) {
        setLockAltKeyFunction(true)
      }
    } else {
      // 如果没有保存状态，设置默认位置为右上角
      this.position = this.getDefaultPosition()
    }
  }

  private getDefaultPosition() {
    // 获取窗口尺寸，设置面板在右上角
    // 假设面板宽度约为 300px，留一些边距
    const panelWidth = 300
    const margin = 20

    return {
      x: window.innerWidth - panelWidth - margin,
      y: margin,
    }
  }

  setIsExpend(value: boolean) {
    this.isExpend = value
    this.saveToLocalStorage()
  }

  setPosition(x: number, y: number) {
    this.position = {x, y}
    this.saveToLocalStorage()
  }

  setLockDeepSelection(lock: boolean) {
    this.lockDeepSelection = lock
    setDeepSelectionKey(lock) // 调用实际的键盘锁定函数
    this.saveToLocalStorage()
  }

  setLockAltKey(lock: boolean) {
    this.lockLockAltKey = lock
    setLockAltKeyFunction(lock) // 调用实际的键盘锁定函数
    this.saveToLocalStorage()
  }

  private saveToLocalStorage() {
    localStorage.setItem(
      'f2c:pannelSettings',
      JSON.stringify({
        isExpend: this.isExpend,
        position: this.position,
        lockDeepSelection: this.lockDeepSelection,
        lockLockAltKey: this.lockLockAltKey,
      }),
    )
  }
}

const panelStore = new PanelStore()
export default panelStore
